package adhoc.logging_monitoring

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

/**
 * Test class to verify LoggingService failures when configuration is null or invalid
 * This class tests direct LoggingService behavior with null configuration
 */
@SpringBootTest(
	classes = [BcmonitoringApplication.class],
	webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class LoggingServiceDirectFailureITSpec extends BaseAdhocITSpec {

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"Token",
			"Account", 
			"Provider"
		])
	}

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Should LoggingService fail when BcmonitoringConfigurationProperties.getEnv() returns null
	 * This test directly creates LoggingService with null env configuration
	 * Expected: NullPointerException when @PostConstruct configureLogLevel() is called
	 */
	def "Should LoggingService fail when configuration env is null"() {
		given: "Configuration properties with null environment"
		def mockConfigProperties = Stub(BcmonitoringConfigurationProperties) {
			getEnv() >> null
		}
		
		when: "Creating LoggingService with null env configuration"
		def testLoggingService = new LoggingService(mockConfigProperties)
		
		// Manually invoke @PostConstruct method to simulate Spring initialization
		def configureLogLevelMethod = LoggingService.class.getDeclaredMethod("configureLogLevel")
		configureLogLevelMethod.setAccessible(true)
		configureLogLevelMethod.invoke(testLoggingService)

		then: "NullPointerException is thrown during @PostConstruct"
		def exception = thrown(java.lang.reflect.InvocationTargetException)
		def rootCause = exception.cause
		rootCause instanceof NullPointerException
		rootCause.message.contains("Cannot invoke \"String.toLowerCase()\"")
		rootCause.message.contains("is null")
		
		println "✅ Successfully demonstrated LoggingService failure with null env"
		println "Root cause: ${rootCause.message}"
	}

	/**
	 * Should LoggingService handle empty string environment gracefully
	 * This test verifies that empty string env does not cause failure
	 * Expected: No exception, falls through to default case (TRACE log level)
	 */
	def "Should LoggingService handle empty string environment gracefully"() {
		given: "Configuration properties with empty environment"
		def mockConfigProperties = Stub(BcmonitoringConfigurationProperties) {
			getEnv() >> ""
		}
		
		when: "Creating LoggingService with empty env configuration"
		def testLoggingService = new LoggingService(mockConfigProperties)
		
		// Manually invoke @PostConstruct method
		def configureLogLevelMethod = LoggingService.class.getDeclaredMethod("configureLogLevel")
		configureLogLevelMethod.setAccessible(true)
		configureLogLevelMethod.invoke(testLoggingService)

		then: "No exception is thrown and default log level is set"
		notThrown(Exception)
		System.getProperty("logging.level.root") == "TRACE" // default case
		
		println "✅ Empty string env handled gracefully - falls to default TRACE level"
	}

	/**
	 * Should LoggingService handle invalid environment values gracefully
	 * This test verifies that unknown env values fall through to default
	 * Expected: No exception, falls through to default case (TRACE log level)
	 */
	def "Should LoggingService handle invalid environment values gracefully"() {
		given: "Configuration properties with invalid environment"
		def mockConfigProperties = Stub(BcmonitoringConfigurationProperties) {
			getEnv() >> "invalid-env"
		}
		
		when: "Creating LoggingService with invalid env configuration"
		def testLoggingService = new LoggingService(mockConfigProperties)
		
		// Manually invoke @PostConstruct method
		def configureLogLevelMethod = LoggingService.class.getDeclaredMethod("configureLogLevel")
		configureLogLevelMethod.setAccessible(true)
		configureLogLevelMethod.invoke(testLoggingService)

		then: "No exception is thrown and default log level is set"
		notThrown(Exception)
		System.getProperty("logging.level.root") == "TRACE" // default case
		
		println "✅ Invalid env value handled gracefully - falls to default TRACE level"
	}

	/**
	 * Should demonstrate the exact scenario when LoggingService initialization fails
	 * This test shows the specific condition that causes LoggingService to fail
	 * Expected: Only null env causes failure, all other values are handled gracefully
	 */
	def "Should demonstrate exact failure scenario for LoggingService"() {
		given: "Different configuration scenarios"
		def scenarios = [
			[description: "null env", env: null, shouldFail: true],
			[description: "empty string env", env: "", shouldFail: false],
			[description: "whitespace env", env: "   ", shouldFail: false],
			[description: "invalid env", env: "invalid", shouldFail: false],
			[description: "valid local env", env: "local", shouldFail: false],
			[description: "valid prod env", env: "prod", shouldFail: false]
		]
		
		when: "Testing each scenario"
		def results = []
		
		scenarios.each { scenario ->
			def mockConfigProperties = Stub(BcmonitoringConfigurationProperties) {
				getEnv() >> scenario.env
			}
			
			def testLoggingService = new LoggingService(mockConfigProperties)
			def configureLogLevelMethod = LoggingService.class.getDeclaredMethod("configureLogLevel")
			configureLogLevelMethod.setAccessible(true)
			
			def failed = false
			def errorMessage = null
			
			try {
				configureLogLevelMethod.invoke(testLoggingService)
			} catch (Exception e) {
				failed = true
				errorMessage = e.cause?.message
			}
			
			results << [
				description: scenario.description,
				env: scenario.env,
				expectedToFail: scenario.shouldFail,
				actuallyFailed: failed,
				errorMessage: errorMessage
			]
		}

		then: "Results match expectations"
		results.each { result ->
			println "Scenario: ${result.description}"
			println "  Env value: '${result.env}'"
			println "  Expected to fail: ${result.expectedToFail}"
			println "  Actually failed: ${result.actuallyFailed}"
			if (result.errorMessage) {
				println "  Error: ${result.errorMessage}"
			}
			println ""
			
			// Verify that expectations match reality
			assert result.expectedToFail == result.actuallyFailed : 
				"Scenario '${result.description}' failed expectation"
		}
		
		// Verify that only null env causes failure
		def nullScenario = results.find { it.description == "null env" }
		assert nullScenario.actuallyFailed : "Null env should cause failure"
		assert nullScenario.errorMessage.contains("Cannot invoke \"String.toLowerCase()\"")
		
		// Verify that all other scenarios pass
		def nonNullScenarios = results.findAll { it.description != "null env" }
		nonNullScenarios.each { scenario ->
			assert !scenario.actuallyFailed : "Non-null env should not cause failure: ${scenario.description}"
		}
		
		println "✅ Successfully demonstrated that ONLY null env causes LoggingService failure"
		println "✅ All other env values (empty, invalid, valid) are handled gracefully"
	}

	/**
	 * Should demonstrate real-world application startup failure with null configuration
	 * This test shows what happens when the application tries to start with null configuration
	 * Expected: Application context fails to load due to LoggingService @PostConstruct failure
	 */
	def "Should demonstrate real-world application startup failure scenario"() {
		given: "Understanding of when LoggingService fails"
		// From previous tests, we know LoggingService only fails when env is null
		// In real Spring Boot applications, this happens when:
		// 1. BcmonitoringConfigurationProperties is not properly configured
		// 2. The env property is not bound from any source (properties files, environment variables, etc.)
		// 3. There's no default value set for the env property
		
		when: "Analyzing the current configuration setup"
		def currentConfig = applicationContext.getBean(BcmonitoringConfigurationProperties.class)
		def currentEnv = currentConfig.getEnv()
		
		then: "Current configuration has valid env value"
		currentEnv != null
		currentEnv instanceof String
		println "Current env value: '${currentEnv}'"
		
		and: "LoggingService is working properly"
		def loggingService = applicationContext.getBean(LoggingService.class)
		loggingService != null
		println "LoggingService is properly initialized"
		
		and: "Understanding when failure would occur"
		println ""
		println "📋 Summary: When does LoggingService initialization fail?"
		println "❌ FAILS: When configProperties.getEnv() returns null"
		println "✅ WORKS: When configProperties.getEnv() returns empty string"
		println "✅ WORKS: When configProperties.getEnv() returns invalid value"
		println "✅ WORKS: When configProperties.getEnv() returns valid value"
		println ""
		println "🔍 Root Cause: NullPointerException in configureLogLevel() method"
		println "   Line: configProperties.getEnv().toLowerCase()"
		println "   When: getEnv() returns null, calling toLowerCase() on null throws NPE"
		println ""
		println "🛠️  How to reproduce in real application:"
		println "   1. Remove env property from all configuration sources"
		println "   2. Remove default value from @ConfigurationProperties"
		println "   3. Ensure no environment variable ENV is set"
		println "   4. Start application - LoggingService @PostConstruct will fail"
		
		true
	}
}
