package adhoc.logging_monitoring

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties
import org.springframework.test.util.ReflectionTestUtils

import static org.mockito.Mockito.doThrow

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles

import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@SpringBootTest(
	classes = [BcmonitoringApplication.class],
	webEnvironment = SpringBootTest.WebEnvironment.NONE,
	properties = "env="
)
@ActiveProfiles("test")
class LoggingServiceFailureITSpec extends BaseAdhocITSpec {

	@Autowired
	ApplicationContext applicationContext

	@Autowired
	BcmonitoringConfigurationProperties properties

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@MockitoSpyBean
	LoggingService loggingService



	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"Token",
			"Account", 
			"Provider"
		])
	}

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Should service run successfully even with configuration challenges
	 * Verifies that the service can handle various configuration scenarios gracefully
	 * Expected: Service starts and runs monitoring successfully
	 */
	def "Should service run successfully with current configuration"() {
		given: "Service with current configuration"
		// Setup mock event stream and pending event
		setUpEventStream(Collections.emptyList())
		setUpPendingEvent(Collections.emptyList())

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 5, TimeUnit.SECONDS)

		commandLineRunner.run("-f")

		then: "Service runs successfully and processes monitoring"
		// Verify that service starts and runs monitoring
		def messages = logAppender.list*.formattedMessage

		// Service should start successfully
		messages.any { it.contains("Started bc monitoring") }

		// Service should process transactions
		messages.any { it.contains("Success to process pending transactions") }

		// LoggingService should be initialized properly
		messages.any { it.contains("Log level set to") }

	}
}
