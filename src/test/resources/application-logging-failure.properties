# Test configuration that causes LoggingService to fail during startup
# This configuration simulates real-world scenarios where environment configuration is missing or invalid

spring.application.name=Bcmonitoring

server.port=2345

# Invalid environment configuration - this will cause LoggingService @PostConstruct to fail
# env property is intentionally set to null to trigger NullPointerException in LoggingService.configureLogLevel()
env=

# AWS Configuration for Test Environment (valid to avoid other failures)
aws.region=ap-northeast-1
aws.access-key-id=test-access-key
aws.secret-access-key=test-secret-key
aws.dynamodb.region=ap-northeast-1
aws.dynamodb.table-prefix=test
aws.dynamodb.endpoint=http://localhost:4566
aws.s3.bucket-name=test-bucket
aws.s3.region=ap-northeast-1
aws.dynamodb.events-table-name=Events
aws.dynamodb.block-height-table-name=BlockHeight

# Ethereum Configuration
ethereum.endpoint=http://localhost:8545

# WebSocket Configuration
websocket.uri.host=localhost
websocket.uri.port=8546

# Subscription Configuration
subscription.check-interval=5000
subscription.allowable-block-timestamp-diff-sec=300

# LocalStack Configuration for Testing
localstack.endpoint=http://localhost:4566
localstack.access-key=test
localstack.secret-key=test
localstack.region=ap-northeast-1

# ABI Format
abi-format=json

# Eager Start
eager-start=false
